package com.sayweee.datasync.fetcher.jira.dto;

import com.sayweee.datasync.model.entity.CommentEntity;
import com.sayweee.datasync.model.entity.IssueEntity;
import com.sayweee.datasync.model.entity.ChangeLogEntity;
import com.sayweee.datasync.model.entity.LinkedIssueEntity;

import java.util.List;

public record JiraIngestionPayload(
        IssueEntity issue,
        List<CommentEntity> comments,
        List<LinkedIssueEntity> linkedIssues
) {}
