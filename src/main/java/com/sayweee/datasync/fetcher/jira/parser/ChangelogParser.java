package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.ChangelogGroup;
import com.atlassian.jira.rest.client.api.domain.ChangelogItem;
import com.atlassian.jira.rest.client.api.domain.Issue;
import com.sayweee.datasync.model.entity.ChangeLogEntity;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class ChangelogParser {

    public List<ChangeLogEntity> parse(Issue issue) {
        List<ChangeLogEntity> changeLogEntities = new ArrayList<>();

        if (issue.getChangelog() == null) {
            return changeLogEntities;
        }

        for (ChangelogGroup changelogGroup : issue.getChangelog()) {
            try {
                List<ChangeLogEntity> groupEntities = parseChangelogGroup(changelogGroup, issue);
                changeLogEntities.addAll(groupEntities);
            } catch (Exception e) {
                log.warn("Failed to parse changelog group for issue {}: {}",
                    issue.getKey(), e.getMessage(), e);
            }
        }

        return changeLogEntities;
    }

    /**
     * 解析单个 ChangelogGroup
     */
    private List<ChangeLogEntity> parseChangelogGroup(ChangelogGroup changelogGroup, Issue issue) {
        List<ChangeLogEntity> entities = new ArrayList<>();

        if (changelogGroup.getItems() == null) {
            return entities;
        }

        for (ChangelogItem item : changelogGroup.getItems()) {
            try {
                ChangeLogEntity entity = parseChangelogItem(item, changelogGroup, issue);
                entities.add(entity);
            } catch (Exception e) {
                log.warn("Failed to parse changelog item {} for issue {}: {}",
                    item.getField(), issue.getKey(), e.getMessage(), e);
            }
        }

        return entities;
    }

    /**
     * 解析单个 ChangelogItem
     */
    private ChangeLogEntity parseChangelogItem(ChangelogItem item, ChangelogGroup group, Issue issue) {
        ChangeLogEntity entity = new ChangeLogEntity();

        // 设置变更ID (尝试获取真实ID，失败则生成)
        entity.setChangeId(getChangelogId(group, issue, item));

        // 设置 Issue Key
        entity.setKey(issue.getKey());

        // 设置项目ID
        if (issue.getProject() != null && issue.getProject().getId() != null) {
            entity.setProjectId(issue.getProject().getId().intValue());
        }

        // 设置用户信息
        if (group.getAuthor() != null) {
            entity.setUserId(group.getAuthor().getAccountId());
            entity.setUsername(group.getAuthor().getDisplayName());
        }

        // 设置变更时间
        entity.setCreated(convertDateTime(group.getCreated()));

        // 设置字段信息
        entity.setField(item.getField());
        entity.setFromValue(item.getFrom());
        entity.setFromString(item.getFromString());
        entity.setToValue(item.getTo());
        entity.setToString(item.getToString());

        // 设置入库时间
        entity.setInDate(LocalDateTime.now());

        return entity;
    }

    /**
     * 获取变更ID，优先尝试获取真实ID，失败则生成
     */
    private Integer getChangelogId(ChangelogGroup group, Issue issue, ChangelogItem item) {
        // 尝试通过反射获取真实的 changelog ID
        Integer realId = tryGetRealChangelogId(group);
        if (realId != null) {
            return realId;
        }

        // 如果无法获取真实ID，则生成一个
        log.debug("Unable to get real changelog ID, generating one for issue {}", issue.getKey());
        return generateChangeId(group, issue, item);
    }

    /**
     * 尝试通过反射获取真实的 changelog ID
     */
    private Integer tryGetRealChangelogId(ChangelogGroup group) {
        try {
            // 打印 ChangelogGroup 的 toString 输出以便调试
            log.debug("ChangelogGroup toString: {}", group.toString());

            // 尝试常见的方法名
            String[] methodNames = {"getId", "id", "getHistoryId", "getChangelogId", "getChangeId"};

            for (String methodName : methodNames) {
                try {
                    var method = group.getClass().getMethod(methodName);
                    Object result = method.invoke(group);
                    if (result != null) {
                        log.debug("Found {} method returning: {}", methodName, result);
                        if (result instanceof Number) {
                            return ((Number) result).intValue();
                        } else if (result instanceof String) {
                            return Integer.parseInt((String) result);
                        }
                    }
                } catch (Exception e) {
                    // 继续尝试下一个方法名
                    log.trace("Method {} not found or failed: {}", methodName, e.getMessage());
                }
            }

            // 如果直接方法不行，尝试查看所有可用方法
            var methods = group.getClass().getMethods();
            for (var method : methods) {
                if (method.getParameterCount() == 0 &&
                    method.getName().toLowerCase().contains("id")) {
                    log.debug("Found potential ID method: {} -> {}", method.getName(), method.getReturnType());
                }
            }

        } catch (Exception e) {
            log.debug("Failed to get real changelog ID via reflection: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 生成变更ID
     * 使用 created 时间戳 + issue key + field + author 的组合哈希值
     */
    private Integer generateChangeId(ChangelogGroup group, Issue issue, ChangelogItem item) {
        StringBuilder sb = new StringBuilder();

        if (group.getCreated() != null) {
            sb.append(group.getCreated().getMillis());
        }

        if (issue.getKey() != null) {
            sb.append(issue.getKey());
        }

        if (item.getField() != null) {
            sb.append(item.getField());
        }

        if (group.getAuthor() != null && group.getAuthor().getAccountId() != null) {
            sb.append(group.getAuthor().getAccountId());
        }

        return Math.abs(sb.toString().hashCode());
    }

    private OffsetDateTime convertDateTime(DateTime dateTime) {
        return dateTime == null ? null :
            OffsetDateTime.ofInstant(Instant.ofEpochMilli(dateTime.getMillis()), ZoneOffset.UTC);
    }
}
