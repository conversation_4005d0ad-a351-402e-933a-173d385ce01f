package com.sayweee.datasync.model.request;

import com.sayweee.datasync.common.enums.SyncMode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;


import java.util.List;
import java.util.Set;

/**
 * Jira数据同步请求对象
 */
@Getter
@Setter
@ToString
public class JiraSyncRequest {
    private Set<String> fields;

    private String startDate;

    /**
     * 结束日期 (UTC时间，格式: yyyy-MM-ddTHH:mm:ssZ 或 yyyy-MM-dd)
     */
    private String endDate;

    /**
     * 同步模式
     */
    private SyncMode syncMode;

    /**
     * 项目列表
     */
    private List<String> projects;

    /**
     * 数据流列表
     */
    private List<String> streams;

    /**
     * 应用默认值规则
     * 在接收到请求后调用此方法设置默认值
     */
    public void applyDefaults() {
        if (StringUtils.isBlank(startDate)) {
            startDate = "-30D";
        }

        syncMode = switch (syncMode) {
            case null -> SyncMode.INCREMENTAL;
            default -> syncMode;
        };

        if (CollectionUtils.isEmpty(streams)) {
            streams = List.of("issues", "projects", "users", "comments");
        }

        if (CollectionUtils.isEmpty(projects)) {
            projects = List.of("PEP");
        }

        if (CollectionUtils.isEmpty(fields)) {
            fields = Set.of("*all");
        }
    }
}